import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Button,
  useTheme,
  useMediaQuery,
  CircularProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  Legend
} from 'recharts';

interface PerformanceCardProps {
  hasData: boolean;
  selectedCourse?: string;
  onPracticeClick?: (courseId: number) => void;
  onExamClick?: (courseId: number) => void;
  performanceData?: {
    correctAnswers: number;
    incorrectAnswers: number;
    examScores: { name: string; score: number }[];
    difficultyBreakdown: { name: string; value: number; color: string }[];
  };
}

const PerformanceCard: React.FC<PerformanceCardProps> = ({
  hasData,
  selectedCourse,
  onPracticeClick,
  onExamClick,
  performanceData = {
    correctAnswers: 0,
    incorrectAnswers: 0,
    examScores: [],
    difficultyBreakdown: []
  }
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const MotionPaper = motion(Paper);

  // Prepare data for pie chart
  const pieData = [
    { name: 'Correct', value: performanceData.correctAnswers, color: theme.palette.success.main },
    { name: 'Incorrect', value: performanceData.incorrectAnswers, color: theme.palette.error.main }
  ].filter(item => item.value > 0);

  // Calculate total questions
  const totalQuestions = performanceData.correctAnswers + performanceData.incorrectAnswers;

  // Calculate accuracy percentage
  const accuracy = totalQuestions > 0
    ? Math.round((performanceData.correctAnswers / totalQuestions) * 100)
    : 0;

  if (!hasData) {
    return (
      <MotionPaper
        variants={itemVariants}
        sx={{
          p: isMobile ? 3 : 4,
          borderRadius: 2,
          maxWidth: '100%',
          textAlign: 'center',
          border: `1px solid ${theme.palette.divider}`
        }}
      >
        <TrendingUpIcon sx={{ fontSize: 60, color: 'primary.main', opacity: 0.5, mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No performance data yet
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Start practicing or take an exam to see your performance statistics.
        </Typography>

        {selectedCourse && (
          <Box sx={{
            mt: 3,
            display: 'flex',
            gap: 2,
            justifyContent: 'center',
            flexDirection: isMobile ? 'column' : 'row'
          }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<PlayArrowIcon />}
              onClick={() => onPracticeClick && onPracticeClick(parseInt(selectedCourse))}
              sx={{ borderRadius: 1.5 }}
            >
              Start Practice
            </Button>
            <Button
              variant="contained"
              color="secondary"
              startIcon={<AssessmentIcon />}
              onClick={() => onExamClick && onExamClick(parseInt(selectedCourse))}
              sx={{ borderRadius: 1.5 }}
            >
              Take Exam
            </Button>
          </Box>
        )}
      </MotionPaper>
    );
  }

  return (
    <MotionPaper
      variants={itemVariants}
      sx={{
        p: isMobile ? 2 : 3,
        borderRadius: 2,
        maxWidth: '100%',
        border: `1px solid ${theme.palette.divider}`
      }}
    >
      <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight="bold" gutterBottom>
        Performance Overview
      </Typography>

      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gap: 2,
        mt: 2
      }}>
        {/* Accuracy Card */}
        <Card
          sx={{
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
            height: '100%'
          }}
        >
          <CardContent sx={{
            p: isMobile ? 2 : 3,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: 'center'
          }}>
            <Box sx={{ position: 'relative', display: 'inline-flex', mb: 1 }}>
              <CircularProgress
                variant="determinate"
                value={accuracy}
                size={isMobile ? 80 : 100}
                thickness={5}
                sx={{
                  color: accuracy >= 70
                    ? theme.palette.success.main
                    : accuracy >= 40
                      ? theme.palette.warning.main
                      : theme.palette.error.main,
                }}
              />
              <Box
                sx={{
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography
                  variant="h5"
                  component="div"
                  fontWeight="bold"
                >
                  {accuracy}%
                </Typography>
              </Box>
            </Box>
            <Typography variant="subtitle1" fontWeight="medium">
              Accuracy
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {performanceData.correctAnswers} correct out of {totalQuestions} questions
            </Typography>
          </CardContent>
        </Card>

        {/* Pie Chart Card */}
        <Card
          sx={{
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
            height: '100%'
          }}
        >
          <CardContent sx={{
            p: isMobile ? 2 : 3,
            height: '100%'
          }}>
            <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
              Question Results
            </Typography>
            <Box sx={{ height: 150, mt: 1 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={60}
                    paddingAngle={5}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    labelLine={false}
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} questions`, '']} />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Exam Scores Chart */}
      {performanceData.examScores.length > 0 && (
        <Card
          sx={{
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
            mt: 2
          }}
        >
          <CardContent sx={{
            p: isMobile ? 2 : 3
          }}>
            <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
              Exam Performance
            </Typography>
            <Box sx={{ height: 200, mt: 1 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={performanceData.examScores}>
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip formatter={(value) => [`${value}%`, 'Score']} />
                  <Bar dataKey="score" fill={theme.palette.primary.main} radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </Card>
      )}
    </MotionPaper>
  );
};

export default PerformanceCard;
