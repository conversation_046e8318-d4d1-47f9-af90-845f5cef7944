import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Chip,
  CircularProgress,
  Alert,
  useTheme,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  SelectChangeEvent,
  Button,
  Collapse,
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  Whatshot as HotstreakIcon,
  Stars as StarsIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Business as DepartmentIcon,
  Book as CourseIcon,
  AccessTime as TimeIcon,
  FilterList as FilterListIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getLeaderboard, LeaderboardParams } from '../../api/gamification';
import { getSchools } from '../../api/schools';
import { getDepartments } from '../../api/departments';
import { getCourses } from '../../api/courses';


interface LeaderboardProps {
  limit?: number;
  initialSchoolId?: number;
  initialDepartmentId?: number;
  initialCourseId?: number;
  initialTimePeriod?: string;
}

const Leaderboard: React.FC<LeaderboardProps> = ({
  limit = 10,
  initialSchoolId,
  initialDepartmentId,
  initialCourseId,
  initialTimePeriod
}) => {
  const theme = useTheme();

  // Filter states
  const [schoolId, setSchoolId] = useState<number | undefined>(initialSchoolId);
  const [departmentId, setDepartmentId] = useState<number | undefined>(initialDepartmentId);
  const [courseId, setCourseId] = useState<number | undefined>(initialCourseId);
  const [timePeriod, setTimePeriod] = useState<string | undefined>(initialTimePeriod);
  const [showMobileFilters, setShowMobileFilters] = useState<boolean>(false);

  // Fetch filter options
  const {
    data: schools,
    isLoading: isLoadingSchools
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools,
  });

  const {
    data: departments,
    isLoading: isLoadingDepartments
  } = useQuery({
    queryKey: ['departments', schoolId],
    queryFn: () => schoolId ? getDepartmentsBySchool(schoolId) : getDepartments(),
    enabled: true, // Always fetch departments, but use schoolId when available
  });

  const {
    data: courses,
    isLoading: isLoadingCourses
  } = useQuery({
    queryKey: ['courses', departmentId],
    queryFn: () => getCourses(undefined, departmentId),
    enabled: true, // Always fetch courses, but filter by departmentId when available
  });

  // Filter handlers
  const handleSchoolChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setSchoolId(value ? Number(value) : undefined);
    setDepartmentId(undefined);
    setCourseId(undefined);
  };

  const handleDepartmentChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setDepartmentId(value ? Number(value) : undefined);
    setCourseId(undefined);
  };

  const handleCourseChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setCourseId(value ? Number(value) : undefined);
  };

  const handleTimePeriodChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setTimePeriod(value || undefined);
  };

  // Prepare leaderboard params
  const leaderboardParams: LeaderboardParams = {
    limit,
    school_id: schoolId,
    department_id: departmentId,
    course_id: courseId,
    time_period: timePeriod as 'daily' | 'weekly' | 'all_time',
  };

  // Fetch leaderboard data
  const { data = { entries: [] }, isLoading, error, refetch } = useQuery({
    queryKey: ['leaderboard', leaderboardParams],
    queryFn: async () => {
      console.log('Fetching leaderboard with params:', leaderboardParams);
      try {
        const result = await getLeaderboard(leaderboardParams);
        console.log('Leaderboard result:', result);
        return result;
      } catch (err) {
        console.error('Error fetching leaderboard:', err);
        throw err;
      }
    },
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    throwOnError: false, // Don't throw errors - handle them silently
  });

  // Prepare the content based on loading/error state
  const renderContent = () => {
    if (isLoading) {
      return (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', p: 4 }}>
          <CircularProgress size={40} sx={{ mb: 2 }} />
          <Typography variant="body1" color="text.secondary">
            Loading leaderboard data...
          </Typography>
        </Box>
      );
    }

    if (error) {
      return (
        <Alert
          severity="error"
          sx={{ mb: 2 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => refetch()}
            >
              Retry
            </Button>
          }
        >
          Error loading leaderboard data: {(error as Error).message || 'Unknown error'}. Please try again.
        </Alert>
      );
    }

    if (!data || data.entries.length === 0) {
      // Determine which filters are active
      const activeFilters = [];
      if (schoolId) activeFilters.push('school');
      if (departmentId) activeFilters.push('department');
      if (courseId) activeFilters.push('course');
      if (timePeriod) activeFilters.push('time period');

      const filterMessage = activeFilters.length > 0
        ? `No leaderboard data available for the selected ${activeFilters.join(', ')} filter${activeFilters.length > 1 ? 's' : ''}.`
        : 'No leaderboard data available. Students need to earn points by completing activities.';

      return (
        <Alert
          severity="info"
          sx={{ mb: 2 }}
          action={
            activeFilters.length > 0 ? (
              <Button
                color="primary"
                size="small"
                onClick={() => {
                  setSchoolId(undefined);
                  setDepartmentId(undefined);
                  setCourseId(undefined);
                  setTimePeriod(undefined);
                }}
              >
                Clear Filters
              </Button>
            ) : undefined
          }
        >
          {filterMessage}
        </Alert>
      );
    }

    return (
      <>
        {/* Desktop View - Table */}
        <Box sx={{ display: { xs: 'none', md: 'block' } }}>
          <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 500, overflow: 'auto' }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>Rank</TableCell>
                  <TableCell>Student</TableCell>
                  <TableCell>School/Department</TableCell>
                  <TableCell>Level</TableCell>
                  <TableCell align="center">Points</TableCell>
                  <TableCell align="center">Badges</TableCell>
                  <TableCell align="center">Streak</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.entries.map((entry, index) => (
                  <TableRow
                    key={entry.user_id}
                    className="leaderboard-row"
                    sx={{
                      backgroundColor: index < 3 ? `${theme.palette.primary.light}10` : 'inherit',
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                      },
                    }}
                  >
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getRankIcon(index)}
                        <Typography variant="body1" fontWeight={index < 3 ? 'bold' : 'normal'} sx={{ ml: 1 }}>
                          {index + 1}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={entry.profile_picture_url || undefined}
                          sx={{ mr: 2, width: 40, height: 40 }}
                        >
                          <PersonIcon />
                        </Avatar>
                        <Typography variant="body1">{entry.full_name}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        {entry.school_name && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <SchoolIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
                            <Typography variant="body2">{entry.school_name}</Typography>
                          </Box>
                        )}
                        {entry.department_name && (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <DepartmentIcon fontSize="small" sx={{ mr: 1, color: theme.palette.secondary.main }} />
                            <Typography variant="body2">{entry.department_name}</Typography>
                          </Box>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={`Level ${entry.level.level_number}`}>
                        <Chip
                          label={entry.level.name}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </Tooltip>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <StarsIcon sx={{ mr: 1, color: theme.palette.warning.main }} fontSize="small" />
                        <Typography variant="body1" fontWeight="medium">
                          {entry.total_points}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <TrophyIcon sx={{ mr: 1, color: theme.palette.success.main }} fontSize="small" />
                        <Typography variant="body1">
                          {entry.badge_count}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <HotstreakIcon sx={{ mr: 1, color: theme.palette.error.main }} fontSize="small" />
                        <Typography variant="body1">
                          {entry.current_streak}
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>

        {/* Mobile View - Card Layout */}
        <Box sx={{ display: { xs: 'block', md: 'none' } }}>
          {data.entries.map((entry, index) => (
            <Paper
              key={entry.user_id}
              elevation={index < 3 ? 3 : 1}
              sx={{
                p: 2,
                mb: 2,
                borderRadius: 2,
                backgroundColor: index < 3 ? `${theme.palette.primary.light}10` : 'inherit',
                border: index < 3 ? `1px solid ${theme.palette.primary.light}` : '1px solid transparent',
                position: 'relative',
                overflow: 'hidden',
                '&::before': index < 3 ? {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '5px',
                  height: '100%',
                  backgroundColor: index === 0
                    ? '#FFD700' // Gold
                    : index === 1
                      ? '#C0C0C0' // Silver
                      : '#CD7F32', // Bronze
                } : {},
              }}
            >
              {/* Card content */}
              {/* ... existing mobile card content ... */}
            </Paper>
          ))}
        </Box>
      </>
    );
  };

  // Get rank icon based on position
  const getRankIcon = (index: number) => {
    switch (index) {
      case 0:
        return <TrophyIcon sx={{ color: '#FFD700' }} />;
      case 1:
        return <TrophyIcon sx={{ color: '#C0C0C0' }} />;
      case 2:
        return <TrophyIcon sx={{ color: '#CD7F32' }} />;
      default:
        return null;
    }
  };

  return (
    <Paper sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <TrophyIcon color="primary" sx={{ mr: 1, fontSize: 28 }} />
          <Typography variant="h5" fontWeight="bold">
            Achievement Leaderboard
          </Typography>
        </Box>

        {/* Active filters display */}
        {(schoolId || departmentId || courseId || timePeriod) && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
            {timePeriod && (
              <Chip
                size="small"
                label={timePeriod === 'daily' ? 'Today' : timePeriod === 'weekly' ? 'This Week' : 'All Time'}
                onDelete={() => setTimePeriod(undefined)}
                color="primary"
                variant="outlined"
                icon={<TimeIcon fontSize="small" />}
              />
            )}

            {schoolId && schools && (
              <Chip
                size="small"
                label={schools.find(s => s.id === schoolId)?.name || 'School'}
                onDelete={() => {
                  setSchoolId(undefined);
                  setDepartmentId(undefined);
                  setCourseId(undefined);
                }}
                color="primary"
                variant="outlined"
                icon={<SchoolIcon fontSize="small" />}
              />
            )}

            {departmentId && departments && (
              <Chip
                size="small"
                label={departments.find(d => d.id === departmentId)?.name || 'Department'}
                onDelete={() => {
                  setDepartmentId(undefined);
                  setCourseId(undefined);
                }}
                color="primary"
                variant="outlined"
                icon={<DepartmentIcon fontSize="small" />}
              />
            )}

            {courseId && courses && (
              <Chip
                size="small"
                label={courses.find(c => c.id === courseId)?.name || 'Course'}
                onDelete={() => setCourseId(undefined)}
                color="primary"
                variant="outlined"
                icon={<CourseIcon fontSize="small" />}
              />
            )}

            {(schoolId || departmentId || courseId || timePeriod) && (
              <Chip
                size="small"
                label="Clear All"
                onClick={() => {
                  setSchoolId(undefined);
                  setDepartmentId(undefined);
                  setCourseId(undefined);
                  setTimePeriod(undefined);
                }}
                color="error"
                variant="outlined"
                icon={<FilterListIcon fontSize="small" />}
              />
            )}
          </Box>
        )}
      </Box>

      {/* Filter controls */}
      <Box sx={{ mb: 3 }}>
        {/* Mobile Filter Toggle */}
        <Box sx={{ display: { xs: 'block', md: 'none' }, mb: 2 }}>
          <Button
            variant="outlined"
            fullWidth
            onClick={() => setShowMobileFilters(prev => !prev)}
            startIcon={showMobileFilters ? <KeyboardArrowUpIcon /> : <FilterListIcon />}
          >
            {showMobileFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
        </Box>

        {/* Desktop Filters - Always visible */}
        <Grid
          container
          spacing={2}
          sx={{
            display: { xs: 'none', md: 'flex' }
          }}
        >
          <Grid item md={3}>
            <FormControl fullWidth size="small">
              <InputLabel id="time-period-label">Time Period</InputLabel>
              <Select
                labelId="time-period-label"
                value={timePeriod || ''}
                label="Time Period"
                onChange={handleTimePeriodChange}
                startAdornment={<TimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />}
              >
                <MenuItem value="">All Time</MenuItem>
                <MenuItem value="daily">Today</MenuItem>
                <MenuItem value="weekly">This Week</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item md={3}>
            <FormControl fullWidth size="small">
              <InputLabel id="school-label">School</InputLabel>
              <Select
                labelId="school-label"
                value={schoolId?.toString() || ''}
                label="School"
                onChange={handleSchoolChange}
                startAdornment={
                  isLoadingSchools
                    ? <CircularProgress size={20} sx={{ mr: 1 }} />
                    : <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                }
                disabled={isLoadingSchools}
              >
                <MenuItem value="">All Schools</MenuItem>
                {schools?.map(school => (
                  <MenuItem key={school.id} value={school.id.toString()}>
                    {school.name}
                  </MenuItem>
                ))}
                {isLoadingSchools && <MenuItem disabled>Loading schools...</MenuItem>}
              </Select>
            </FormControl>
          </Grid>

          <Grid item md={3}>
            <FormControl fullWidth size="small" disabled={!schoolId}>
              <InputLabel id="department-label">Department</InputLabel>
              <Select
                labelId="department-label"
                value={departmentId?.toString() || ''}
                label="Department"
                onChange={handleDepartmentChange}
                startAdornment={
                  isLoadingDepartments
                    ? <CircularProgress size={20} sx={{ mr: 1 }} />
                    : <DepartmentIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                }
                disabled={!schoolId || isLoadingDepartments}
              >
                <MenuItem value="">All Departments</MenuItem>
                {departments?.map(department => (
                  <MenuItem key={department.id} value={department.id.toString()}>
                    {department.name}
                  </MenuItem>
                ))}
                {isLoadingDepartments && <MenuItem disabled>Loading departments...</MenuItem>}
                {!isLoadingDepartments && departments?.length === 0 &&
                  <MenuItem disabled>No departments found</MenuItem>
                }
              </Select>
            </FormControl>
          </Grid>

          <Grid item md={3}>
            <FormControl fullWidth size="small" disabled={!departmentId}>
              <InputLabel id="course-label">Course</InputLabel>
              <Select
                labelId="course-label"
                value={courseId?.toString() || ''}
                label="Course"
                onChange={handleCourseChange}
                startAdornment={
                  isLoadingCourses
                    ? <CircularProgress size={20} sx={{ mr: 1 }} />
                    : <CourseIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                }
                disabled={!departmentId || isLoadingCourses}
              >
                <MenuItem value="">All Courses</MenuItem>
                {courses?.map(course => (
                  <MenuItem key={course.id} value={course.id.toString()}>
                    {course.name} ({course.code})
                  </MenuItem>
                ))}
                {isLoadingCourses && <MenuItem disabled>Loading courses...</MenuItem>}
                {!isLoadingCourses && courses?.length === 0 &&
                  <MenuItem disabled>No courses found</MenuItem>
                }
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Mobile Filters - Collapsible */}
        <Collapse in={showMobileFilters} sx={{ display: { xs: 'block', md: 'none' } }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth size="small">
                <InputLabel id="mobile-time-period-label">Time Period</InputLabel>
                <Select
                  labelId="mobile-time-period-label"
                  value={timePeriod || ''}
                  label="Time Period"
                  onChange={handleTimePeriodChange}
                  startAdornment={<TimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  <MenuItem value="">All Time</MenuItem>
                  <MenuItem value="daily">Today</MenuItem>
                  <MenuItem value="weekly">This Week</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth size="small">
                <InputLabel id="mobile-school-label">School</InputLabel>
                <Select
                  labelId="mobile-school-label"
                  value={schoolId?.toString() || ''}
                  label="School"
                  onChange={handleSchoolChange}
                  startAdornment={
                    isLoadingSchools
                      ? <CircularProgress size={20} sx={{ mr: 1 }} />
                      : <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  }
                  disabled={isLoadingSchools}
                >
                  <MenuItem value="">All Schools</MenuItem>
                  {schools?.map(school => (
                    <MenuItem key={school.id} value={school.id.toString()}>
                      {school.name}
                    </MenuItem>
                  ))}
                  {isLoadingSchools && <MenuItem disabled>Loading schools...</MenuItem>}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth size="small" disabled={!schoolId}>
                <InputLabel id="mobile-department-label">Department</InputLabel>
                <Select
                  labelId="mobile-department-label"
                  value={departmentId?.toString() || ''}
                  label="Department"
                  onChange={handleDepartmentChange}
                  startAdornment={
                    isLoadingDepartments
                      ? <CircularProgress size={20} sx={{ mr: 1 }} />
                      : <DepartmentIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  }
                  disabled={!schoolId || isLoadingDepartments}
                >
                  <MenuItem value="">All Departments</MenuItem>
                  {departments?.map(department => (
                    <MenuItem key={department.id} value={department.id.toString()}>
                      {department.name}
                    </MenuItem>
                  ))}
                  {isLoadingDepartments && <MenuItem disabled>Loading departments...</MenuItem>}
                  {!isLoadingDepartments && departments?.length === 0 &&
                    <MenuItem disabled>No departments found</MenuItem>
                  }
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth size="small" disabled={!departmentId}>
                <InputLabel id="mobile-course-label">Course</InputLabel>
                <Select
                  labelId="mobile-course-label"
                  value={courseId?.toString() || ''}
                  label="Course"
                  onChange={handleCourseChange}
                  startAdornment={
                    isLoadingCourses
                      ? <CircularProgress size={20} sx={{ mr: 1 }} />
                      : <CourseIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  }
                  disabled={!departmentId || isLoadingCourses}
                >
                  <MenuItem value="">All Courses</MenuItem>
                  {courses?.map(course => (
                    <MenuItem key={course.id} value={course.id.toString()}>
                      {course.name} ({course.code})
                    </MenuItem>
                  ))}
                  {isLoadingCourses && <MenuItem disabled>Loading courses...</MenuItem>}
                  {!isLoadingCourses && courses?.length === 0 &&
                    <MenuItem disabled>No courses found</MenuItem>
                  }
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Collapse>
      </Box>

      {/* Desktop View - Table */}
      <Box sx={{ display: { xs: 'none', md: 'block' } }}>
        <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 500, overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Rank</TableCell>
                <TableCell>Student</TableCell>
                <TableCell>School/Department</TableCell>
                <TableCell>Level</TableCell>
                <TableCell align="center">Points</TableCell>
                <TableCell align="center">Badges</TableCell>
                <TableCell align="center">Streak</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.entries.map((entry, index) => (
                <TableRow
                  key={entry.user_id}
                  className="leaderboard-row"
                  sx={{
                    backgroundColor: index < 3 ? `${theme.palette.primary.light}10` : 'inherit',
                    '&:hover': {
                      backgroundColor: theme.palette.action.hover,
                    },
                  }}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {getRankIcon(index)}
                      <Typography variant="body1" fontWeight={index < 3 ? 'bold' : 'normal'} sx={{ ml: 1 }}>
                        {index + 1}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        src={entry.profile_picture_url || undefined}
                        sx={{ mr: 2, width: 40, height: 40 }}
                      >
                        <PersonIcon />
                      </Avatar>
                      <Typography variant="body1">{entry.full_name}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      {entry.school_name && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <SchoolIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
                          <Typography variant="body2">{entry.school_name}</Typography>
                        </Box>
                      )}
                      {entry.department_name && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <DepartmentIcon fontSize="small" sx={{ mr: 1, color: theme.palette.secondary.main }} />
                          <Typography variant="body2">{entry.department_name}</Typography>
                        </Box>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Tooltip title={`Level ${entry.level.level_number}`}>
                      <Chip
                        label={entry.level.name}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    </Tooltip>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <StarsIcon sx={{ mr: 1, color: theme.palette.warning.main }} fontSize="small" />
                      <Typography variant="body1" fontWeight="medium">
                        {entry.total_points}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <TrophyIcon sx={{ mr: 1, color: theme.palette.success.main }} fontSize="small" />
                      <Typography variant="body1">
                        {entry.badge_count}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <HotstreakIcon sx={{ mr: 1, color: theme.palette.error.main }} fontSize="small" />
                      <Typography variant="body1">
                        {entry.current_streak}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Mobile View - Card Layout */}
      <Box sx={{ display: { xs: 'block', md: 'none' } }}>
        {data.entries.map((entry, index) => (
          <Paper
            key={entry.user_id}
            elevation={index < 3 ? 3 : 1}
            sx={{
              p: 2,
              mb: 2,
              borderRadius: 2,
              backgroundColor: index < 3 ? `${theme.palette.primary.light}10` : 'inherit',
              border: index < 3 ? `1px solid ${theme.palette.primary.light}` : '1px solid transparent',
              position: 'relative',
              overflow: 'hidden',
              '&::before': index < 3 ? {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '5px',
                height: '100%',
                backgroundColor: index === 0
                  ? '#FFD700' // Gold
                  : index === 1
                    ? '#C0C0C0' // Silver
                    : '#CD7F32', // Bronze
              } : {},
            }}
          >
            <Grid container spacing={1}>
              {/* Rank and User Info */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 36,
                    height: 36,
                    borderRadius: '50%',
                    backgroundColor: index === 0
                      ? '#FFD700' // Gold
                      : index === 1
                        ? '#C0C0C0' // Silver
                        : index === 2
                          ? '#CD7F32' // Bronze
                          : theme.palette.grey[200],
                    mr: 2,
                    boxShadow: index < 3 ? '0 2px 4px rgba(0,0,0,0.2)' : 'none',
                  }}>
                    <Typography variant="body1" fontWeight="bold" color={index < 3 ? 'rgba(0,0,0,0.7)' : 'text.primary'}>
                      {index + 1}
                    </Typography>
                  </Box>
                  <Avatar
                    src={entry.profile_picture_url || undefined}
                    sx={{
                      mr: 2,
                      width: 45,
                      height: 45,
                      border: index < 3 ? `2px solid ${
                        index === 0
                          ? '#FFD700' // Gold
                          : index === 1
                            ? '#C0C0C0' // Silver
                            : '#CD7F32' // Bronze
                      }` : 'none',
                    }}
                  >
                    <PersonIcon />
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body1" fontWeight="medium">{entry.full_name}</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      <Chip
                        label={entry.level.name}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                      {index < 3 && (
                        <Tooltip title={index === 0 ? "1st Place" : index === 1 ? "2nd Place" : "3rd Place"}>
                          <TrophyIcon
                            sx={{
                              ml: 1,
                              color: index === 0
                                ? '#FFD700' // Gold
                                : index === 1
                                  ? '#C0C0C0' // Silver
                                  : '#CD7F32' // Bronze
                            }}
                            fontSize="small"
                          />
                        </Tooltip>
                      )}
                    </Box>
                  </Box>
                </Box>
              </Grid>

              {/* School/Department */}
              {(entry.school_name || entry.department_name) && (
                <Grid item xs={12}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    mb: 1,
                    pl: 2,
                    gap: 1,
                  }}>
                    {entry.school_name && (
                      <Chip
                        icon={<SchoolIcon fontSize="small" />}
                        label={entry.school_name}
                        size="small"
                        variant="outlined"
                        sx={{
                          borderColor: theme.palette.primary.main,
                          '& .MuiChip-icon': {
                            color: theme.palette.primary.main
                          }
                        }}
                      />
                    )}
                    {entry.department_name && (
                      <Chip
                        icon={<DepartmentIcon fontSize="small" />}
                        label={entry.department_name}
                        size="small"
                        variant="outlined"
                        sx={{
                          borderColor: theme.palette.secondary.main,
                          '& .MuiChip-icon': {
                            color: theme.palette.secondary.main
                          }
                        }}
                      />
                    )}
                  </Box>
                </Grid>
              )}

              {/* Stats */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 1, borderTop: `1px solid ${theme.palette.divider}`, pt: 1 }}>
                  <Tooltip title="Points">
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: `${theme.palette.warning.main}10`,
                      borderRadius: 1,
                      p: 1,
                      minWidth: 60,
                    }}>
                      <StarsIcon sx={{ color: theme.palette.warning.main }} fontSize="small" />
                      <Typography variant="body1" fontWeight="medium" sx={{ mt: 0.5 }}>
                        {entry.total_points}
                      </Typography>
                    </Box>
                  </Tooltip>

                  <Tooltip title="Badges">
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: `${theme.palette.success.main}10`,
                      borderRadius: 1,
                      p: 1,
                      minWidth: 60,
                    }}>
                      <TrophyIcon sx={{ color: theme.palette.success.main }} fontSize="small" />
                      <Typography variant="body1" sx={{ mt: 0.5 }}>
                        {entry.badge_count}
                      </Typography>
                    </Box>
                  </Tooltip>

                  <Tooltip title="Streak">
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: `${theme.palette.error.main}10`,
                      borderRadius: 1,
                      p: 1,
                      minWidth: 60,
                    }}>
                      <HotstreakIcon sx={{ color: theme.palette.error.main }} fontSize="small" />
                      <Typography variant="body1" sx={{ mt: 0.5 }}>
                        {entry.current_streak}
                      </Typography>
                    </Box>
                  </Tooltip>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        ))}
      </Box>
    </Paper>
  );
};

export default Leaderboard;
