import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  IconButton,
  Avatar,
  useTheme,
  alpha,
  Button,
  Collapse
} from '@mui/material';
import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Logout as LogoutIcon,
  Person as PersonIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon
} from '@mui/icons-material';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useThemeMode } from '../../contexts/ThemeContext';

interface MobileMenuProps {
  open: boolean;
  onClose: () => void;
  menuItems: Array<{
    text: string;
    icon: React.ReactNode;
    path: string;
  }>;
  onLogout: () => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  open,
  onClose,
  menuItems,
  onLogout
}) => {
  const theme = useTheme();
  const location = useLocation();
  const { user } = useAuth();
  const { mode, toggleColorMode } = useThemeMode();
  const isDarkMode = mode === 'dark';
  const [settingsOpen, setSettingsOpen] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07,
        delayChildren: 0.2
      }
    },
    exit: {
      opacity: 0,
      transition: {
        staggerChildren: 0.05,
        staggerDirection: -1
      }
    }
  };

  const itemVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 24
      }
    },
    exit: {
      x: -20,
      opacity: 0
    }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.3 }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.3 }
    }
  };

  const drawerVariants = {
    hidden: { x: '-100%' },
    visible: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <AnimatePresence>
      {open && (
        <>
          <motion.div
            key="backdrop"
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: alpha(theme.palette.common.black, 0.5),
              backdropFilter: 'blur(4px)',
              zIndex: 1200
            }}
            onClick={onClose}
          />

          <motion.div
            key="drawer"
            variants={drawerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              bottom: 0,
              width: '85%',
              maxWidth: '320px',
              background: theme.palette.background.paper,
              zIndex: 1300,
              borderRadius: '0 16px 16px 0',
              boxShadow: theme.shadows[10],
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {/* Header */}
            <Box
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                borderBottom: `1px solid ${theme.palette.divider}`,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                color: 'white'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    width: 40,
                    height: 40,
                    bgcolor: 'white',
                    color: theme.palette.primary.main,
                    fontWeight: 'bold',
                    mr: 1.5
                  }}
                >
                  {user?.full_name?.charAt(0).toUpperCase() || 'C'}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {user?.full_name || 'CampusPQ'}
                  </Typography>
                  <Typography variant="caption">
                    {user?.email || 'Welcome!'}
                  </Typography>
                </Box>
              </Box>
              <IconButton
                onClick={onClose}
                sx={{
                  color: 'white',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.2)'
                  }
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>

            {/* Menu Items */}
            <Box sx={{ overflow: 'auto', flex: 1, py: 1 }}>
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <List sx={{ px: 1 }}>
                  {menuItems.map((item, index) => (
                    <motion.div key={item.text} variants={itemVariants} custom={index}>
                      <ListItem
                        button
                        component={RouterLink}
                        to={item.path}
                        onClick={onClose}
                        sx={{
                          borderRadius: 2,
                          mb: 0.5,
                          py: 1.5,
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            left: 0,
                            top: 0,
                            bottom: 0,
                            width: 4,
                            bgcolor: location.pathname === item.path
                              ? theme.palette.primary.main
                              : 'transparent',
                            borderRadius: '0 4px 4px 0',
                            transition: 'all 0.2s ease'
                          },
                          bgcolor: location.pathname === item.path
                            ? alpha(theme.palette.primary.main, 0.1)
                            : 'transparent',
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            '&::before': {
                              width: 4,
                              bgcolor: theme.palette.primary.main,
                              opacity: 0.5
                            }
                          }
                        }}
                      >
                        <ListItemIcon
                          sx={{
                            color: location.pathname === item.path
                              ? theme.palette.primary.main
                              : theme.palette.text.secondary,
                            minWidth: 40
                          }}
                        >
                          {item.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={item.text}
                          primaryTypographyProps={{
                            fontWeight: location.pathname === item.path ? 'bold' : 'medium'
                          }}
                        />
                      </ListItem>
                    </motion.div>
                  ))}
                </List>
              </motion.div>
            </Box>

            {/* Footer */}
            <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
              <List sx={{ p: 0 }}>
                <ListItem
                  button
                  onClick={() => setSettingsOpen(!settingsOpen)}
                  sx={{
                    borderRadius: 2,
                    py: 1.5,
                    transition: 'all 0.3s ease',
                    bgcolor: settingsOpen ? alpha(theme.palette.primary.main, 0.12) : 'transparent',
                    border: settingsOpen ? `1px solid ${alpha(theme.palette.primary.main, 0.2)}` : '1px solid transparent',
                    '&:hover': {
                      bgcolor: settingsOpen
                        ? alpha(theme.palette.primary.main, 0.18)
                        : alpha(theme.palette.primary.main, 0.08),
                      transform: 'translateY(-1px)',
                      boxShadow: settingsOpen
                        ? `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                        : `0 2px 8px ${alpha(theme.palette.common.black, 0.1)}`
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    color: settingsOpen ? theme.palette.primary.main : theme.palette.text.secondary,
                    transition: 'all 0.3s ease',
                    minWidth: 40
                  }}>
                    <motion.div
                      animate={{
                        scale: settingsOpen ? 1.1 : 1,
                        rotate: settingsOpen ? 360 : 0
                      }}
                      transition={{ duration: 0.3, type: 'spring' }}
                    >
                      <PersonIcon />
                    </motion.div>
                  </ListItemIcon>
                  <ListItemText
                    primary="Settings"
                    primaryTypographyProps={{
                      fontWeight: settingsOpen ? 'bold' : 'medium',
                      fontSize: '1rem',
                      transition: 'all 0.3s ease'
                    }}
                  />
                  <motion.div
                    animate={{
                      rotate: settingsOpen ? 180 : 0,
                      scale: settingsOpen ? 1.1 : 1
                    }}
                    transition={{ duration: 0.3, type: 'spring', stiffness: 300 }}
                    style={{
                      color: settingsOpen ? theme.palette.primary.main : theme.palette.text.secondary
                    }}
                  >
                    <ExpandMoreIcon />
                  </motion.div>
                </ListItem>

                <Collapse in={settingsOpen} timeout="auto" unmountOnExit>
                  <Box sx={{
                    mt: 1,
                    mx: 1,
                    p: 1.5,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.background.default, 0.5),
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                  }}>
                    {/* Profile Section */}
                    <ListItem
                      button
                      component={RouterLink}
                      to="/profile"
                      onClick={onClose}
                      sx={{
                        borderRadius: 1.5,
                        mb: 1,
                        py: 1.5,
                        px: 2,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.08),
                          transform: 'translateX(4px)'
                        }
                      }}
                    >
                      <ListItemIcon sx={{
                        minWidth: 36,
                        color: theme.palette.text.secondary
                      }}>
                        <PersonIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Profile"
                        primaryTypographyProps={{
                          fontSize: '0.9rem',
                          fontWeight: 500
                        }}
                      />
                    </ListItem>

                    {/* Theme Toggle Section */}
                    <ListItem
                      button
                      onClick={toggleColorMode}
                      sx={{
                        borderRadius: 1.5,
                        py: 1.5,
                        px: 2,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.08),
                          transform: 'translateX(4px)'
                        }
                      }}
                    >
                      <ListItemIcon sx={{
                        minWidth: 36,
                        color: isDarkMode ? theme.palette.warning.main : theme.palette.primary.main
                      }}>
                        <motion.div
                          key={isDarkMode ? "dark" : "light"}
                          initial={{ scale: 0.8, rotate: -180 }}
                          animate={{ scale: 1, rotate: 0 }}
                          transition={{ duration: 0.3, type: 'spring' }}
                        >
                          {isDarkMode ?
                            <LightModeIcon fontSize="small" /> :
                            <DarkModeIcon fontSize="small" />
                          }
                        </motion.div>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <motion.span
                            key={isDarkMode ? "dark" : "light"}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.2 }}
                            style={{
                              fontSize: '0.9rem',
                              fontWeight: 500
                            }}
                          >
                            {isDarkMode ? "Light Mode" : "Dark Mode"}
                          </motion.span>
                        }
                      />
                    </ListItem>
                  </Box>
                </Collapse>

                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  style={{ marginTop: '8px' }}
                >
                  <ListItem
                    button
                    onClick={onLogout}
                    sx={{
                      borderRadius: 2,
                      py: 1.5,
                      color: theme.palette.error.main,
                      border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: alpha(theme.palette.error.main, 0.1),
                        borderColor: alpha(theme.palette.error.main, 0.4),
                        boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.2)}`
                      }
                    }}
                  >
                    <ListItemIcon sx={{
                      color: 'inherit',
                      minWidth: 40
                    }}>
                      <motion.div
                        whileHover={{
                          rotate: [0, -15, 15, -15, 0],
                          scale: 1.1
                        }}
                        transition={{ duration: 0.5 }}
                      >
                        <LogoutIcon />
                      </motion.div>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1" fontWeight="medium" fontSize="1rem">
                          Logout
                        </Typography>
                      }
                    />
                  </ListItem>
                </motion.div>
              </List>
            </Box>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default MobileMenu;
