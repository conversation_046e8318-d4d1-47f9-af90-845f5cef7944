import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  OutlinedInput,
  CircularProgress,
  Alert,
  Snackbar,
  Switch,
  FormControlLabel,
  InputAdornment,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  getMyTutorProfile,
  createMyTutorProfile,
  updateMyTutorProfile,
  TutorProfileCreate,
  TutorProfileUpdate,
} from '../../api/tutors';
import { getCourses, Course } from '../../api/courses';

const validationSchema = Yup.object({
  bio: Yup.string().max(1000, 'Bio must be at most 1000 characters'),
  experience_years: Yup.number()
    .min(0, 'Experience years must be at least 0')
    .max(50, 'Experience years must be at most 50')
    .nullable(),
  hourly_rate: Yup.number()
    .min(0, 'Hourly rate must be at least 0')
    .nullable(),
  preferred_session_type: Yup.string().oneOf(['online', 'in_person', 'both']),
  max_students_per_session: Yup.number()
    .min(1, 'Must allow at least 1 student')
    .max(20, 'Cannot exceed 20 students per session')
    .required('Required'),
  linkedin_url: Yup.string().url('Must be a valid URL').nullable(),
  website_url: Yup.string().url('Must be a valid URL').nullable(),
  specialization_ids: Yup.array().min(1, 'Please select at least one specialization'),
});

const TutorProfileForm: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch existing profile
  const {
    data: existingProfile,
    isLoading: isLoadingProfile,
    error: profileError,
  } = useQuery({
    queryKey: ['tutorProfile'],
    queryFn: getMyTutorProfile,
    retry: false,
  });

  // Fetch courses for specializations
  const {
    data: courses = [],
    isLoading: isLoadingCourses,
  } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  const isEditMode = !!existingProfile;

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: TutorProfileCreate) => createMyTutorProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tutorProfile'] });
      setSuccessMessage('Tutor profile created successfully');
      setTimeout(() => {
        navigate('/tutor/dashboard');
      }, 1500);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: TutorProfileUpdate) => updateMyTutorProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tutorProfile'] });
      setSuccessMessage('Tutor profile updated successfully');
      setTimeout(() => {
        navigate('/tutor/dashboard');
      }, 1500);
    },
  });

  const formik = useFormik({
    initialValues: {
      bio: '',
      experience_years: '',
      hourly_rate: '',
      is_available: true,
      preferred_session_type: '',
      max_students_per_session: 1,
      linkedin_url: '',
      website_url: '',
      specialization_ids: [] as number[],
    },
    validationSchema,
    onSubmit: (values) => {
      const data = {
        ...values,
        experience_years: values.experience_years ? Number(values.experience_years) : undefined,
        hourly_rate: values.hourly_rate ? Number(values.hourly_rate) : undefined,
        linkedin_url: values.linkedin_url || undefined,
        website_url: values.website_url || undefined,
        preferred_session_type: values.preferred_session_type || undefined,
      };

      if (isEditMode) {
        updateMutation.mutate(data);
      } else {
        createMutation.mutate(data);
      }
    },
  });

  // Update form values when existing profile is loaded
  useEffect(() => {
    if (existingProfile) {
      formik.setValues({
        bio: existingProfile.bio || '',
        experience_years: existingProfile.experience_years?.toString() || '',
        hourly_rate: existingProfile.hourly_rate?.toString() || '',
        is_available: existingProfile.is_available,
        preferred_session_type: existingProfile.preferred_session_type || '',
        max_students_per_session: existingProfile.max_students_per_session,
        linkedin_url: existingProfile.linkedin_url || '',
        website_url: existingProfile.website_url || '',
        specialization_ids: existingProfile.specializations?.map(s => s.id) || [],
      });
    }
  }, [existingProfile]);

  if (isLoadingProfile || isLoadingCourses) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (profileError && !profileError.message.includes('404')) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading profile: {profileError.message}
      </Alert>
    );
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/tutor/dashboard')}
          sx={{ mr: 2 }}
        >
          Back to Dashboard
        </Button>
        
        <Typography variant="h4" component="h1">
          {isEditMode ? 'Edit Tutor Profile' : 'Create Tutor Profile'}
        </Typography>
      </Box>

      {!isEditMode && (
        <Alert severity="info" sx={{ mb: 3 }}>
          New to tutoring? Try our{' '}
          <Button
            variant="text"
            size="small"
            onClick={() => navigate('/complete-tutor-profile')}
            sx={{ textDecoration: 'underline' }}
          >
            guided setup wizard
          </Button>
          {' '}for a step-by-step experience.
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            {/* Bio */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                name="bio"
                label="Bio"
                placeholder="Tell students about yourself, your teaching style, and experience..."
                value={formik.values.bio}
                onChange={formik.handleChange}
                error={formik.touched.bio && Boolean(formik.errors.bio)}
                helperText={formik.touched.bio && formik.errors.bio}
              />
            </Grid>

            {/* Experience and Rate */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                name="experience_years"
                label="Years of Experience"
                value={formik.values.experience_years}
                onChange={formik.handleChange}
                error={formik.touched.experience_years && Boolean(formik.errors.experience_years)}
                helperText={formik.touched.experience_years && formik.errors.experience_years}
                inputProps={{ min: 0, max: 50 }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                name="hourly_rate"
                label="Hourly Rate"
                value={formik.values.hourly_rate}
                onChange={formik.handleChange}
                error={formik.touched.hourly_rate && Boolean(formik.errors.hourly_rate)}
                helperText={formik.touched.hourly_rate && formik.errors.hourly_rate}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                inputProps={{ min: 0, step: 0.01 }}
              />
            </Grid>

            {/* Session Preferences */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Preferred Session Type</InputLabel>
                <Select
                  name="preferred_session_type"
                  value={formik.values.preferred_session_type}
                  onChange={formik.handleChange}
                  label="Preferred Session Type"
                >
                  <MenuItem value="online">Online</MenuItem>
                  <MenuItem value="in_person">In Person</MenuItem>
                  <MenuItem value="both">Both</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                name="max_students_per_session"
                label="Max Students per Session"
                value={formik.values.max_students_per_session}
                onChange={formik.handleChange}
                error={formik.touched.max_students_per_session && Boolean(formik.errors.max_students_per_session)}
                helperText={formik.touched.max_students_per_session && formik.errors.max_students_per_session}
                inputProps={{ min: 1, max: 20 }}
              />
            </Grid>

            {/* Specializations */}
            <Grid item xs={12}>
              <FormControl fullWidth error={formik.touched.specialization_ids && Boolean(formik.errors.specialization_ids)}>
                <InputLabel>Specializations (Courses you can teach)</InputLabel>
                <Select
                  multiple
                  name="specialization_ids"
                  value={formik.values.specialization_ids}
                  onChange={formik.handleChange}
                  input={<OutlinedInput label="Specializations (Courses you can teach)" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as number[]).map((value) => {
                        const course = courses.find(c => c.id === value);
                        return <Chip key={value} label={course?.name || value} size="small" />;
                      })}
                    </Box>
                  )}
                >
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name} ({course.code})
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.specialization_ids && formik.errors.specialization_ids && (
                  <FormHelperText>{formik.errors.specialization_ids}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* Social Links */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="linkedin_url"
                label="LinkedIn URL"
                value={formik.values.linkedin_url}
                onChange={formik.handleChange}
                error={formik.touched.linkedin_url && Boolean(formik.errors.linkedin_url)}
                helperText={formik.touched.linkedin_url && formik.errors.linkedin_url}
                placeholder="https://linkedin.com/in/yourprofile"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="website_url"
                label="Website URL"
                value={formik.values.website_url}
                onChange={formik.handleChange}
                error={formik.touched.website_url && Boolean(formik.errors.website_url)}
                helperText={formik.touched.website_url && formik.errors.website_url}
                placeholder="https://yourwebsite.com"
              />
            </Grid>

            {/* Availability */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    name="is_available"
                    checked={formik.values.is_available}
                    onChange={formik.handleChange}
                  />
                }
                label="Available for new sessions"
              />
            </Grid>

            {/* Error Display */}
            {error && (
              <Grid item xs={12}>
                <Alert severity="error">
                  Error: {error.message}
                </Alert>
              </Grid>
            )}

            {/* Submit Button */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={isSubmitting ? <CircularProgress size={20} /> : <SaveIcon />}
                  disabled={isSubmitting}
                  size="large"
                >
                  {isSubmitting ? 'Saving...' : (isEditMode ? 'Update Profile' : 'Create Profile')}
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={() => navigate('/tutor/dashboard')}
                  disabled={isSubmitting}
                  size="large"
                >
                  Cancel
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>

      {/* Success Snackbar */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
      >
        <Alert onClose={() => setSuccessMessage(null)} severity="success">
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TutorProfileForm;
