import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  Divider,
  Button,
  useTheme,
  IconButton,
  Tabs,
  Tab,
  Avatar,
  Chip,
  useMediaQuery,
  SwipeableDrawer
} from '@mui/material';
import {
  Link as RouterLink,
  useNavigate
} from 'react-router-dom';
import {
  ArrowForward as ArrowForwardIcon,
  School as SchoolIcon,
  Event as EventIcon,
  Book as BookIcon,
  QuestionAnswer as QuestionIcon,
  Quiz as QuizIcon,
  Notifications as NotificationsIcon,
  TrendingUp as TrendingUpIcon,
  EmojiEvents as TrophyIcon,
  Dashboard as DashboardIcon,
  ViewCarousel as CardIcon,
  CheckCircle as CheckCircleIcon,
  Bookmark as BookmarkIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { getCourses, getUserCourses, Course } from '../api/courses';
import { getSessions, Session } from '../api/sessions';
import { getUserPoints } from '../api/gamification';
import { getMCQStats, getBookmarks, Bookmark } from '../api/studentProgress';
import { motion } from 'framer-motion';
import StudentProgressCard from '../components/StudentProgressCard';
import PopularToolsSection from '../components/Dashboard/PopularToolsSection';
import RecentActivitySection from '../components/Dashboard/RecentActivitySection';
import { MLRecommendationsDashboard } from '../components/MLRecommendations';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [loading, setLoading] = useState(true);
  const [courses, setCourses] = useState<Course[]>([]);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [tabValue, setTabValue] = useState(0);

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  const MotionPaper = motion(Paper);
  const MotionCard = motion(Card);

  // Fetch user points (for students only)
  const {
    data: userPoints = 0,
  } = useQuery({
    queryKey: ['userPoints', user?.id],
    queryFn: () => getUserPoints(),
    enabled: !!user && user.role === 'student'
  });

  // Fetch MCQ stats (for students only)
  const {
    data: mcqStats,
  } = useQuery({
    queryKey: ['mcqStats', user?.id],
    queryFn: () => getMCQStats(),
    enabled: !!user && user.role === 'student'
  });

  // Fetch recent bookmarks
  const {
    data: bookmarks = [],
    isLoading: isLoadingBookmarks
  } = useQuery({
    queryKey: ['bookmarks', user?.id],
    queryFn: () => getBookmarks(user!.id),
    enabled: !!user && user.role === 'student'
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Get courses the user has interacted with (enrolled, practiced, taken exams, used flashcards)
        const [coursesData, sessionsData] = await Promise.all([
          getUserCourses(), // Returns courses from enrollment, exams, practice, flashcards
          getSessions(),
        ]);

        setCourses(coursesData);
        setSessions(sessionsData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.id]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Filter sessions based on user role
  const relevantSessions = user?.role === 'tutor'
    ? sessions.filter(session => session.tutor_id === user.id)
    : sessions; // Students can see all sessions, not just enrolled ones

  // Get upcoming sessions (future sessions)
  const upcomingSessions = relevantSessions.filter(
    session => new Date(session.start_time) > new Date()
  );

  // Sort sessions: For students, prioritize joined tutorials first, then by start time
  const sortedSessions = user?.role === 'student'
    ? [...upcomingSessions].sort((a, b) => {
        // First, prioritize sessions the student has joined
        const aIsJoined = a.student_id === user.id;
        const bIsJoined = b.student_id === user.id;

        if (aIsJoined && !bIsJoined) return -1;
        if (!aIsJoined && bIsJoined) return 1;

        // Then sort by start time (earliest first)
        return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
      })
    : [...upcomingSessions].sort(
        (a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
      );

  return (
    <Box sx={{
      maxWidth: '100%',
      overflow: 'hidden',
      width: '100%'
    }}>
      {/* Header with welcome message and user info */}
      <Box
        sx={{
          mb: { xs: 2, sm: 4 },
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          justifyContent: 'space-between',
          alignItems: isMobile ? 'flex-start' : 'center',
          gap: 2
        }}
      >
        <Box>
          <Typography
            variant={isMobile ? "body1" : "h6"}
            color="text.secondary"
            gutterBottom
          >
            Welcome back, {user?.full_name}!
          </Typography>
        </Box>

        {/* User stats for mobile */}
        {isMobile && user?.role === 'student' && (
          <Box
            sx={{
              display: 'flex',
              gap: 1,
              width: '100%',
              justifyContent: 'flex-start',
              flexWrap: 'wrap'
            }}
          >
            <Chip
              icon={<TrophyIcon />}
              label={`${userPoints} Points`}
              color="primary"
              variant="outlined"
              onClick={() => navigate('/achievement')}
              sx={{ borderRadius: 1 }}
            />
            <Chip
              icon={<QuizIcon />}
              label={`${mcqStats?.total_exams || 0} Quizzes`}
              color="secondary"
              variant="outlined"
              onClick={() => navigate('/mcq')}
              sx={{ borderRadius: 1 }}
            />
          </Box>
        )}
      </Box>

      {/* Mobile Tabs Navigation */}
      {isMobile && (
        <Paper
          sx={{
            mb: 2,
            borderRadius: 2,
            overflow: 'hidden'
          }}
          elevation={0}
        >
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            textColor="primary"
            indicatorColor="primary"
            aria-label="dashboard tabs"
          >
            <Tab
              label="Overview"
              icon={<DashboardIcon />}
              iconPosition="start"
              sx={{
                minHeight: '48px',
                fontSize: '0.875rem',
                textTransform: 'none'
              }}
            />
            <Tab
              label="Courses"
              icon={<BookIcon />}
              iconPosition="start"
              sx={{
                minHeight: '48px',
                fontSize: '0.875rem',
                textTransform: 'none'
              }}
            />
            <Tab
              label="Tutorials"
              icon={<EventIcon />}
              iconPosition="start"
              sx={{
                minHeight: '48px',
                fontSize: '0.875rem',
                textTransform: 'none'
              }}
            />
          </Tabs>
        </Paper>
      )}

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: isMobile ? '1fr' : '2fr 1fr',
            gap: isMobile ? 2 : 3
          }}
        >
        {/* Left Column - or full width on mobile depending on selected tab */}
        <Box
          sx={{
            display: isMobile ?
              (tabValue === 0 || tabValue === 1 ? 'block' : 'none') :
              'block'
          }}
        >
          <MotionPaper
              variants={itemVariants}
              sx={{
                p: isMobile ? 2 : 3,
                mb: isMobile ? 2 : 3,
                borderRadius: 2,
                maxWidth: '100%',
              }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: isMobile ? 1.5 : 2
                }}
              >
                <Typography
                  variant={isMobile ? "subtitle1" : "h6"}
                  fontWeight="bold"
                >
                  Your Courses
                </Typography>

                <Button
                  component={RouterLink}
                  to="/courses"
                  endIcon={<ArrowForwardIcon />}
                  size="small"
                  sx={{
                    minWidth: isMobile ? 'auto' : undefined,
                    px: isMobile ? 1 : undefined
                  }}
                >
                  {isMobile ? '' : 'View All'}
                  {isMobile && <ArrowForwardIcon fontSize="small" />}
                </Button>
              </Box>

            {courses.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="body1" color="text.secondary">
                  No courses yet. Start by enrolling in courses, taking practice questions, or using study tools to see your courses here.
                </Typography>
                <Button
                  component={RouterLink}
                  to="/courses"
                  variant="contained"
                  sx={{ mt: 2 }}
                >
                  Browse Courses
                </Button>
              </Box>
            ) : (
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                  gap: isMobile ? 1.5 : 2
                }}
              >
                {courses.slice(0, isMobile ? 2 : 4).map((course) => (
                  <Box key={course.id} sx={{ maxWidth: '100%' }}>
                    <MotionCard
                      variants={itemVariants}
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'transform 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                        }
                      }}
                    >
                      <CardContent sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        p: isMobile ? 2 : 3,
                        '&:last-child': { pb: isMobile ? 2 : 3 }
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Avatar
                            sx={{
                              width: 32,
                              height: 32,
                              mr: 1.5,
                              bgcolor: theme.palette.primary.main,
                              color: '#fff',
                              fontSize: '0.875rem'
                            }}
                          >
                            {course.name.charAt(0)}
                          </Avatar>
                          <Typography
                            variant={isMobile ? "subtitle1" : "h6"}
                            component="div"
                            fontWeight="medium"
                            noWrap
                          >
                            {course.name}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {course.code}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            mb: 2,
                            flex: 1,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}
                        >
                          {course.description.substring(0, isMobile ? 60 : 100)}
                          {course.description.length > (isMobile ? 60 : 100) ? '...' : ''}
                        </Typography>
                        <Button
                          component={RouterLink}
                          to={`/courses/${course.id}`}
                          size="small"
                          variant="outlined"
                          endIcon={<ArrowForwardIcon />}
                          sx={{
                            alignSelf: 'flex-start',
                            borderRadius: 1.5
                          }}
                        >
                          View Course
                        </Button>
                      </CardContent>
                    </MotionCard>
                  </Box>
                ))}
              </Box>
            )}

            {courses.length > (isMobile ? 2 : 4) && (
              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Button
                  component={RouterLink}
                  to="/courses"
                  endIcon={<ArrowForwardIcon />}
                  size="small"
                  sx={{ borderRadius: 1.5 }}
                >
                  View All Courses
                </Button>
              </Box>
            )}
          </MotionPaper>

          {user?.role === 'student' && (
            <>
              {/* ML Recommendations Dashboard */}
              <Box sx={{
                mb: isMobile ? 2 : 3,
                maxWidth: '100%',
                overflow: 'hidden'
              }}>
                <MLRecommendationsDashboard
                  maxRecommendations={3}
                  showInsights={true}
                  showProgress={true}
                  compact={isMobile}
                />
              </Box>

              {/* Student Progress Card */}
              <Box sx={{ mb: isMobile ? 2 : 3 }}>
                <StudentProgressCard userId={user?.id} />
              </Box>

              {/* Popular and New Tools Section */}
              <PopularToolsSection />

              {/* Recent Activity */}
              <RecentActivitySection />
            </>
          )}

          {user?.role === 'tutor' && (
            <MotionPaper
              variants={itemVariants}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                maxWidth: '100%',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Your Questions
                </Typography>

                <Button
                  component={RouterLink}
                  to="/questions"
                  endIcon={<ArrowForwardIcon />}
                  size="small"
                >
                  View All
                </Button>
              </Box>

              <Box sx={{ textAlign: 'center', py: 3 }}>
                <QuestionIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2, opacity: 0.7 }} />
                <Typography variant="body1" color="text.secondary" paragraph>
                  Create and manage questions for your courses.
                </Typography>
                <Button
                  component={RouterLink}
                  to="/questions/new"
                  variant="contained"
                  endIcon={<ArrowForwardIcon />}
                >
                  Create Question
                </Button>
              </Box>
            </MotionPaper>
          )}

          {user?.role === 'admin' && (
            <MotionPaper
              variants={itemVariants}
              sx={{
                p: 3,
                mb: 3,
                borderRadius: 2,
                maxWidth: '100%',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" fontWeight="bold">
                  System Overview
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', sm: 'repeat(3, 1fr)' },
                  gap: 2
                }}
              >
                <Box sx={{ maxWidth: '100%' }}>
                  <MotionCard
                    variants={itemVariants}
                    sx={{
                      borderLeft: `4px solid ${theme.palette.primary.main}`,
                      height: '100%'
                    }}
                  >
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Courses
                      </Typography>
                      <Typography variant="h4" component="div" fontWeight="bold">
                        {courses.length}
                      </Typography>
                    </CardContent>
                  </MotionCard>
                </Box>
                <Box sx={{ maxWidth: '100%' }}>
                  <MotionCard
                    variants={itemVariants}
                    sx={{
                      borderLeft: `4px solid ${theme.palette.secondary.main}`,
                      height: '100%'
                    }}
                  >
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Tutorials
                      </Typography>
                      <Typography variant="h4" component="div" fontWeight="bold">
                        {sessions.length}
                      </Typography>
                    </CardContent>
                  </MotionCard>
                </Box>
                <Box sx={{ maxWidth: '100%' }}>
                  <MotionCard
                    variants={itemVariants}
                    sx={{
                      borderLeft: `4px solid ${theme.palette.info.main}`,
                      height: '100%'
                    }}
                  >
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Users
                      </Typography>
                      <Typography variant="h4" component="div" fontWeight="bold">
                        -
                      </Typography>
                    </CardContent>
                  </MotionCard>
                </Box>
              </Box>
            </MotionPaper>
          )}
        </Box>

        {/* Right Column - or full width on mobile depending on selected tab */}
        <Box
          sx={{
            display: isMobile ?
              (tabValue === 0 || tabValue === 2 ? 'block' : 'none') :
              'block'
          }}
        >
          {/* Recent Bookmarks Section - Student Only */}
          {user?.role === 'student' && (
            <MotionPaper
              variants={itemVariants}
              sx={{
                p: isMobile ? 2 : 3,
                mb: isMobile ? 2 : 3,
                borderRadius: 2,
                maxWidth: '100%',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: isMobile ? 1.5 : 2
                }}
              >
                <Typography
                  variant={isMobile ? "subtitle1" : "h6"}
                  fontWeight="bold"
                >
                  Recent Bookmarks
                </Typography>

                <Button
                  component={RouterLink}
                  to="/mcq/bookmarks"
                  endIcon={<ArrowForwardIcon />}
                  size="small"
                  sx={{
                    minWidth: isMobile ? 'auto' : undefined,
                    px: isMobile ? 1 : undefined
                  }}
                >
                  {isMobile ? '' : 'View All'}
                  {isMobile && <ArrowForwardIcon fontSize="small" />}
                </Button>
              </Box>

              {isLoadingBookmarks ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                  <CircularProgress size={24} />
                </Box>
              ) : bookmarks.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: isMobile ? 2 : 3 }}>
                  <BookmarkIcon sx={{ fontSize: 48, color: 'warning.main', mb: 2, opacity: 0.7 }} />
                  <Typography variant="body1" color="text.secondary" paragraph>
                    No bookmarked questions yet.
                  </Typography>
                  <Button
                    component={RouterLink}
                    to="/mcq"
                    variant="contained"
                    sx={{ mt: 1, borderRadius: 1.5 }}
                  >
                    Start Practicing
                  </Button>
                </Box>
              ) : (
                <List sx={{ px: isMobile ? 0 : 1 }}>
                  {bookmarks.slice(0, isMobile ? 2 : 3).map((bookmark, index) => (
                    <React.Fragment key={bookmark.id}>
                      {index > 0 && <Divider />}
                      <ListItem
                        component={RouterLink}
                        to={`/mcq/bookmarks`}
                        sx={{
                          textDecoration: 'none',
                          color: 'inherit',
                          borderRadius: 1,
                          py: 1.5,
                          px: isMobile ? 1 : 2,
                          '&:hover': {
                            backgroundColor: theme.palette.mode === 'light'
                              ? 'rgba(0, 0, 0, 0.04)'
                              : 'rgba(255, 255, 255, 0.04)',
                          },
                        }}
                      >
                        <Avatar
                          sx={{
                            mr: 2,
                            bgcolor: 'warning.main',
                            width: isMobile ? 32 : 40,
                            height: isMobile ? 32 : 40,
                          }}
                        >
                          <BookmarkIcon fontSize={isMobile ? 'small' : 'medium'} />
                        </Avatar>
                        <ListItemText
                          primary={
                            <Typography
                              variant={isMobile ? "body2" : "body1"}
                              fontWeight="medium"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                              }}
                            >
                              Question #{bookmark.question_id}
                            </Typography>
                          }
                          secondary={
                            <Box sx={{ mt: 0.5 }}>
                              <Typography
                                variant="caption"
                                sx={{
                                  display: 'block',
                                  color: theme.palette.warning.main,
                                  fontWeight: 'medium'
                                }}
                              >
                                Bookmarked: {new Date(bookmark.created_at).toLocaleDateString()}
                              </Typography>
                              {bookmark.notes && (
                                <Typography variant="caption" color="text.secondary">
                                  {bookmark.notes.length > 50 ? `${bookmark.notes.substring(0, 50)}...` : bookmark.notes}
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    </React.Fragment>
                  ))}
                </List>
              )}

              {bookmarks.length > (isMobile ? 2 : 3) && (
                <Box sx={{ textAlign: 'center', mt: 2 }}>
                  <Button
                    component={RouterLink}
                    to="/mcq/bookmarks"
                    endIcon={<ArrowForwardIcon />}
                    size="small"
                    sx={{ borderRadius: 1.5 }}
                  >
                    View {bookmarks.length - (isMobile ? 2 : 3)} More Bookmarks
                  </Button>
                </Box>
              )}
            </MotionPaper>
          )}
          <MotionPaper
            variants={itemVariants}
            sx={{
              p: isMobile ? 2 : 3,
              mb: isMobile ? 2 : 3,
              borderRadius: 2,
              maxWidth: '100%',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: isMobile ? 1.5 : 2
              }}
            >
              <Typography
                variant={isMobile ? "subtitle1" : "h6"}
                fontWeight="bold"
              >
                Upcoming Tutorials
              </Typography>

              <Button
                component={RouterLink}
                to="/tutorials"
                endIcon={<ArrowForwardIcon />}
                size="small"
                sx={{
                  minWidth: isMobile ? 'auto' : undefined,
                  px: isMobile ? 1 : undefined
                }}
              >
                {isMobile ? '' : 'View All'}
                {isMobile && <ArrowForwardIcon fontSize="small" />}
              </Button>
            </Box>

            {sortedSessions.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: isMobile ? 2 : 3 }}>
                <EventIcon sx={{ fontSize: 48, color: 'info.main', mb: 2, opacity: 0.7 }} />
                <Typography variant="body1" color="text.secondary" paragraph>
                  No upcoming tutorials.
                </Typography>
                {user?.role === 'student' ? (
                  <Button
                    component={RouterLink}
                    to="/tutorials"
                    variant="contained"
                    sx={{ mt: 1, borderRadius: 1.5 }}
                  >
                    Join a Tutorial
                  </Button>
                ) : (
                  <Button
                    component={RouterLink}
                    to="/sessions/new"
                    variant="contained"
                    sx={{ mt: 1, borderRadius: 1.5 }}
                  >
                    Create a Tutorial
                  </Button>
                )}
              </Box>
            ) : (
              <List sx={{ px: isMobile ? 0 : 1 }}>
                {sortedSessions.slice(0, isMobile ? 3 : 5).map((session, index) => {
                  const isJoined = user?.role === 'student' && session.student_id === user.id;
                  return (
                    <React.Fragment key={session.id}>
                      {index > 0 && <Divider />}
                      <ListItem
                        component={RouterLink}
                        to={`/tutorials/${session.id}`}
                        sx={{
                          textDecoration: 'none',
                          color: 'inherit',
                          borderRadius: 1,
                          py: 1.5,
                          px: isMobile ? 1 : 2,
                          backgroundColor: isJoined
                            ? theme.palette.mode === 'light'
                              ? 'rgba(25, 118, 210, 0.08)'
                              : 'rgba(144, 202, 249, 0.08)'
                            : 'transparent',
                          border: isJoined
                            ? `1px solid ${theme.palette.primary.main}20`
                            : 'none',
                          '&:hover': {
                            backgroundColor: isJoined
                              ? theme.palette.mode === 'light'
                                ? 'rgba(25, 118, 210, 0.12)'
                                : 'rgba(144, 202, 249, 0.12)'
                              : theme.palette.mode === 'light'
                                ? 'rgba(0, 0, 0, 0.04)'
                                : 'rgba(255, 255, 255, 0.04)',
                          },
                        }}
                      >
                      <Avatar
                        sx={{
                          bgcolor: isJoined
                            ? theme.palette.success.main
                            : theme.palette.info.main,
                          mr: 2,
                          width: 40,
                          height: 40,
                          display: { xs: 'none', sm: 'flex' }
                        }}
                      >
                        {isJoined ? <CheckCircleIcon /> : <EventIcon />}
                      </Avatar>
                      <ListItemText
                        primary={
                          <Typography
                            variant="subtitle2"
                            fontWeight="medium"
                            sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 1,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                          >
                            {session.title}
                          </Typography>
                        }
                        secondary={
                          <Box sx={{ mt: 0.5 }}>
                            <Typography
                              variant="caption"
                              sx={{
                                display: 'block',
                                color: theme.palette.info.main,
                                fontWeight: 'medium'
                              }}
                            >
                              {new Date(session.start_time).toLocaleDateString()} • {new Date(session.start_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {session.session_type === 'online' ? 'Online' : `In-person: ${session.location}`}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    </React.Fragment>
                  );
                })}
              </List>
            )}

            {sortedSessions.length > (isMobile ? 3 : 5) && (
              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Button
                  component={RouterLink}
                  to="/tutorials"
                  endIcon={<ArrowForwardIcon />}
                  size="small"
                  sx={{ borderRadius: 1.5 }}
                >
                  View All Tutorials
                </Button>
              </Box>
            )}
          </MotionPaper>
        </Box>
      </Box>
      </motion.div>
    </Box>
  );
};

export default Dashboard;
