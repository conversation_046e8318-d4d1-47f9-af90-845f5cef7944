from datetime import datetime, timezone
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session

from app import schemas
from app.models.student_progress import (
    StudentQuestionAttempt, StudentBookmark, StudentExam, StudentExamAttempt
)
from app.services import gamification_service


# Question Attempt functions
def create_question_attempt(
    db: Session, *, obj_in: schemas.StudentQuestionAttemptCreate
) -> StudentQuestionAttempt:
    db_obj = StudentQuestionAttempt(
        student_id=obj_in.student_id,
        question_id=obj_in.question_id,
        is_correct=obj_in.is_correct,
        selected_answer=obj_in.selected_answer,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)

    # Update user streak
    gamification_service.update_streak(db, user_id=obj_in.student_id)

    # Award points for practice
    points = gamification_service.POINTS["CORRECT_ANSWER"] if obj_in.is_correct else 0
    if points > 0:
        gamification_service.award_points(
            db=db,
            user_id=obj_in.student_id,
            points=points,
            description="Answered a question correctly",
            transaction_type="practice_correct_answer",
            reference_id=db_obj.id
        )

    # Check for practice achievements
    gamification_service.check_for_practice_achievements(db, user_id=obj_in.student_id)

    return db_obj


def get_student_question_attempts(
    db: Session, *, student_id: int, question_id: Optional[int] = None
) -> List[StudentQuestionAttempt]:
    query = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == student_id
    )

    if question_id:
        query = query.filter(StudentQuestionAttempt.question_id == question_id)

    return query.order_by(StudentQuestionAttempt.attempt_time.desc()).all()


def get_student_course_attempts(
    db: Session, *, student_id: int, course_id: int
) -> List[StudentQuestionAttempt]:
    from app.models.question import Question
    return (
        db.query(StudentQuestionAttempt)
        .join(Question, StudentQuestionAttempt.question_id == Question.id)
        .filter(
            StudentQuestionAttempt.student_id == student_id,
            Question.course_id == course_id
        )
        .order_by(StudentQuestionAttempt.attempt_time.desc())
        .all()
    )


# Bookmark functions
def create_bookmark(
    db: Session, *, obj_in: schemas.StudentBookmarkCreate
) -> StudentBookmark:
    db_obj = StudentBookmark(
        student_id=obj_in.student_id,
        question_id=obj_in.question_id,
        notes=obj_in.notes,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_bookmark(db: Session, *, id: int) -> Optional[StudentBookmark]:
    return db.query(StudentBookmark).filter(StudentBookmark.id == id).first()


def get_student_bookmarks(
    db: Session, *, student_id: int, course_id: Optional[int] = None
) -> List[StudentBookmark]:
    query = db.query(StudentBookmark).filter(
        StudentBookmark.student_id == student_id
    )

    if course_id:
        from app.models.question import Question
        query = query.join(Question, StudentBookmark.question_id == Question.id).filter(
            Question.course_id == course_id
        )

    return query.order_by(StudentBookmark.created_at.desc()).all()


def update_bookmark(
    db: Session, *, db_obj: StudentBookmark, obj_in: schemas.StudentBookmarkUpdate
) -> StudentBookmark:
    if obj_in.notes is not None:
        db_obj.notes = obj_in.notes

    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_bookmark(db: Session, *, id: int) -> StudentBookmark:
    obj = db.query(StudentBookmark).get(id)
    db.delete(obj)
    db.commit()
    return obj


# Exam functions
def create_exam(
    db: Session, *, obj_in: schemas.StudentExamCreate
) -> StudentExam:
    # Create the exam object with all fields from the input
    db_obj = StudentExam(
        student_id=obj_in.student_id,
        course_id=obj_in.course_id,
        total_questions=obj_in.total_questions,
        score=obj_in.score,
        correct_answers=obj_in.correct_answers,
        end_time=obj_in.end_time,
    )

    # Add note-generated content fields if present and if the columns exist
    try:
        if obj_in.is_note_generated:
            # Try to set the fields, but catch any exceptions if the columns don't exist
            try:
                db_obj.is_note_generated = True
            except Exception as e:
                print(f"Warning: Could not set is_note_generated field: {str(e)}")

            try:
                db_obj.note_job_id = obj_in.note_job_id
            except Exception as e:
                print(f"Warning: Could not set note_job_id field: {str(e)}")

            try:
                db_obj.course_name = obj_in.course_name
            except Exception as e:
                print(f"Warning: Could not set course_name field: {str(e)}")
    except Exception as e:
        print(f"Warning: Could not set note-generated fields: {str(e)}")
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)

    # Update user streak
    gamification_service.update_streak(db, user_id=obj_in.student_id)

    # Award points for exam completion
    base_points = gamification_service.POINTS["EXAM_COMPLETION"]

    # Add bonus points based on score
    score_bonus = 0
    if obj_in.score is not None:
        score_bonus = int(obj_in.score / 10)  # 0-10 bonus points based on score percentage

    # Perfect score bonus
    perfect_bonus = 0
    if obj_in.score == 100.0:
        perfect_bonus = gamification_service.POINTS["PERFECT_SCORE"]

    total_points = base_points + score_bonus + perfect_bonus

    gamification_service.award_points(
        db=db,
        user_id=obj_in.student_id,
        points=total_points,
        description=f"Completed exam with score {obj_in.score}%",
        transaction_type="exam_completion",
        reference_id=db_obj.id
    )

    # Check for exam achievements
    gamification_service.check_for_exam_achievements(db, user_id=obj_in.student_id, exam_id=db_obj.id)

    return db_obj


def get_exam(db: Session, *, id: int) -> Optional[StudentExam]:
    return db.query(StudentExam).filter(StudentExam.id == id).first()


def get_student_exams(
    db: Session, *, student_id: int, course_id: Optional[int] = None
) -> List[StudentExam]:
    query = db.query(StudentExam).filter(
        StudentExam.student_id == student_id
    )

    if course_id:
        query = query.filter(StudentExam.course_id == course_id)

    return query.order_by(StudentExam.start_time.desc()).all()


def update_exam(
    db: Session, *, db_obj: StudentExam, obj_in: schemas.StudentExamUpdate
) -> StudentExam:
    # Store original state to check if exam is being completed
    was_completed = db_obj.end_time is not None

    if obj_in.score is not None:
        db_obj.score = obj_in.score
    if obj_in.correct_answers is not None:
        db_obj.correct_answers = obj_in.correct_answers
    if obj_in.end_time is not None:
        db_obj.end_time = obj_in.end_time
    else:
        db_obj.end_time = datetime.now(timezone.utc)

    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)

    # If the exam is being completed (wasn't completed before but now has an end_time)
    if not was_completed and db_obj.end_time is not None and db_obj.score is not None:
        # Update user streak
        gamification_service.update_streak(db, user_id=db_obj.student_id)

        # Award points for exam completion
        base_points = gamification_service.POINTS["EXAM_COMPLETION"]

        # Add bonus points based on score
        score_bonus = int(db_obj.score / 10)  # 0-10 bonus points based on score percentage

        # Perfect score bonus
        perfect_bonus = 0
        if db_obj.score == 100.0:
            perfect_bonus = gamification_service.POINTS["PERFECT_SCORE"]

        total_points = base_points + score_bonus + perfect_bonus

        gamification_service.award_points(
            db=db,
            user_id=db_obj.student_id,
            points=total_points,
            description=f"Completed exam with score {db_obj.score}%",
            transaction_type="exam_completion",
            reference_id=db_obj.id
        )

        # Check for exam achievements
        gamification_service.check_for_exam_achievements(db, user_id=db_obj.student_id, exam_id=db_obj.id)

    return db_obj


def create_exam_attempt(
    db: Session, *, obj_in: schemas.StudentExamAttemptCreate
) -> StudentExamAttempt:
    print(f"Creating exam attempt in service: {obj_in}")
    try:
        db_obj = StudentExamAttempt(
            exam_id=obj_in.exam_id,
            question_id=obj_in.question_id,
            is_correct=obj_in.is_correct,
            selected_answer=obj_in.selected_answer,
            time_spent_seconds=obj_in.time_spent_seconds,
        )
        print(f"Created StudentExamAttempt object: {db_obj}")
        db.add(db_obj)
        print(f"Added to session")
        db.commit()
        print(f"Committed to database")
        db.refresh(db_obj)
        print(f"Refreshed object, id: {db_obj.id}")
        return db_obj
    except Exception as e:
        print(f"Error creating exam attempt: {str(e)}")
        db.rollback()
        raise


def get_exam_with_attempts(db: Session, *, id: int) -> Optional[schemas.StudentExamWithAttempts]:
    print(f"Service: get_exam_with_attempts for id={id}")

    exam = db.query(StudentExam).filter(StudentExam.id == id).first()
    if not exam:
        print(f"Service: Exam not found with id={id}")
        return None

    print(f"Service: Found exam with id={exam.id}, student_id={exam.student_id}, course_id={exam.course_id}")
    print(f"Service: Exam details: score={exam.score}, correct_answers={exam.correct_answers}, total_questions={exam.total_questions}")

    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == id
    ).all()

    print(f"Service: Found {len(attempts)} attempts for exam id={id}")
    for i, attempt in enumerate(attempts):
        print(f"Service: Attempt {i+1}: question_id={attempt.question_id}, is_correct={attempt.is_correct}")

    result = schemas.StudentExamWithAttempts(
        **{k: v for k, v in exam.__dict__.items() if not k.startswith('_')},
        attempts=attempts
    )

    print(f"Service: Returning exam with {len(result.attempts)} attempts")
    return result


def get_exam_topic_analysis(db: Session, *, exam_id: int) -> Dict[str, Any]:
    """
    Get topic-based analysis for an exam.
    Returns a dictionary with topic statistics including:
    - correct_count: number of correct answers for the topic
    - total_count: total number of questions for the topic
    - score: percentage score for the topic
    - avg_time_spent: average time spent on questions for this topic (in seconds)
    """
    from app.models.question import Question

    # Get the exam with attempts
    exam = get_exam_with_attempts(db, id=exam_id)
    if not exam:
        return {}

    # Get all question IDs from the attempts
    question_ids = [attempt.question_id for attempt in exam.attempts]

    # Get the questions with their topics
    questions = db.query(Question).filter(Question.id.in_(question_ids)).all()

    # Create a mapping of question_id to topic
    question_topic_map = {q.id: q.topic if q.topic else "Uncategorized" for q in questions}

    # Initialize topic analysis
    topic_analysis = {}

    # Process each attempt
    for attempt in exam.attempts:
        question_id = attempt.question_id
        topic = question_topic_map.get(question_id, "Uncategorized")

        # Initialize topic data if not exists
        if topic not in topic_analysis:
            topic_analysis[topic] = {
                "correct_count": 0,
                "total_count": 0,
                "time_spent_total": 0,
                "score": 0.0,
                "avg_time_spent": 0
            }

        # Update topic statistics
        topic_analysis[topic]["total_count"] += 1
        if attempt.is_correct:
            topic_analysis[topic]["correct_count"] += 1

        # Add time spent if available
        if hasattr(attempt, 'time_spent_seconds') and attempt.time_spent_seconds:
            topic_analysis[topic]["time_spent_total"] += attempt.time_spent_seconds

    # Calculate percentages and averages
    for topic, data in topic_analysis.items():
        if data["total_count"] > 0:
            data["score"] = (data["correct_count"] / data["total_count"]) * 100
            if data["time_spent_total"] > 0:
                data["avg_time_spent"] = data["time_spent_total"] / data["total_count"]

    return topic_analysis


def get_course_topic_analysis(db: Session, *, student_id: int, course_id: int) -> Dict[str, Any]:
    """
    Get topic-based analysis for all exams in a course for a student.
    Returns a dictionary with topic statistics across all exams.
    """
    # Get all exams for the student in the course
    exams = get_student_exams(db, student_id=student_id, course_id=course_id)
    if not exams:
        return {}

    # Initialize topic analysis
    topic_analysis = {}

    # Process each exam
    for exam in exams:
        exam_analysis = get_exam_topic_analysis(db, exam_id=exam.id)

        # Merge exam analysis into overall analysis
        for topic, data in exam_analysis.items():
            if topic not in topic_analysis:
                topic_analysis[topic] = {
                    "correct_count": 0,
                    "total_count": 0,
                    "time_spent_total": 0,
                    "exams_count": 0,
                    "score": 0.0,
                    "avg_time_spent": 0
                }

            # Update topic statistics
            topic_analysis[topic]["correct_count"] += data["correct_count"]
            topic_analysis[topic]["total_count"] += data["total_count"]
            topic_analysis[topic]["time_spent_total"] += data.get("time_spent_total", 0)
            topic_analysis[topic]["exams_count"] += 1

    # Calculate percentages and averages
    for topic, data in topic_analysis.items():
        if data["total_count"] > 0:
            data["score"] = (data["correct_count"] / data["total_count"]) * 100
            if data["time_spent_total"] > 0:
                data["avg_time_spent"] = data["time_spent_total"] / data["total_count"]

    return topic_analysis


def get_user_mcq_stats(db: Session, *, user_id: int) -> Dict[str, Any]:
    """
    Get MCQ statistics for a user including total exams, average score, and practice questions.
    """
    from sqlalchemy import func
    from app.models.student_progress import StudentExam, StudentQuestionAttempt

    # Get exam statistics
    exam_stats = db.query(
        func.count(StudentExam.id).label('total_exams'),
        func.avg(StudentExam.score).label('average_score')
    ).filter(
        StudentExam.student_id == user_id,
        StudentExam.score.isnot(None)  # Only completed exams
    ).first()

    # Get practice question statistics
    practice_stats = db.query(
        func.count(StudentQuestionAttempt.id).label('total_practice_questions')
    ).filter(
        StudentQuestionAttempt.student_id == user_id
    ).first()

    return {
        'total_exams': exam_stats.total_exams or 0,
        'average_score': round(exam_stats.average_score or 0, 1),
        'total_practice_questions': practice_stats.total_practice_questions or 0
    }


def get_user_performance_data(db: Session, *, user_id: int) -> Dict[str, Any]:
    """
    Get performance data for a user including correct/incorrect answers and exam scores.
    """
    from sqlalchemy import func
    from app.models.student_progress import StudentExam, StudentQuestionAttempt

    # Get practice question statistics
    practice_correct = db.query(func.count(StudentQuestionAttempt.id)).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.is_correct == True
    ).scalar() or 0

    practice_incorrect = db.query(func.count(StudentQuestionAttempt.id)).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.is_correct == False
    ).scalar() or 0

    # Get recent exam scores (last 10 exams)
    recent_exams = db.query(StudentExam).filter(
        StudentExam.student_id == user_id,
        StudentExam.score.isnot(None)
    ).order_by(StudentExam.start_time.desc()).limit(10).all()

    exam_scores = []
    for i, exam in enumerate(recent_exams):
        exam_scores.append({
            'name': f'Exam {len(recent_exams) - i}',
            'score': round(exam.score, 1)
        })

    # Reverse to show chronological order
    exam_scores.reverse()

    return {
        'correctAnswers': practice_correct,
        'incorrectAnswers': practice_incorrect,
        'examScores': exam_scores,
        'difficultyBreakdown': []  # Can be implemented later if needed
    }
