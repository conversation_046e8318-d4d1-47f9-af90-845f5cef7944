import logging
import time

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1.api import api_router
from app.core.config import settings
from app.core.startup import initialize_application, handle_startup_failure, get_application_status
from app.middleware import LoggingMiddleware, RateLimiter, FileUploadConfig
from app.middleware.timeout_middleware import TimeoutMiddleware, DatabaseTimeoutMiddleware
from app.middleware.monitoring_middleware import (
    MonitoringMiddleware,
    HangingDetectionMiddleware,
    ResourceMonitoringMiddleware
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AI-driven learning ecosystem for students, tutors, and administrators",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_PREFIX}/openapi.json",
    docs_url=f"{settings.API_V1_PREFIX}/docs",
    redoc_url=f"{settings.API_V1_PREFIX}/redoc",
    # Add timeout configurations
    timeout=30.0,  # 30 second request timeout
)

# Set up middlewares (order matters - monitoring and timeout should be first)
app.add_middleware(MonitoringMiddleware)  # Request monitoring
app.add_middleware(HangingDetectionMiddleware, hanging_threshold=60.0)  # Hanging detection
app.add_middleware(ResourceMonitoringMiddleware, memory_threshold_mb=100.0)  # Resource monitoring
app.add_middleware(TimeoutMiddleware, timeout_seconds=30.0)  # Global request timeout
app.add_middleware(DatabaseTimeoutMiddleware, db_timeout_seconds=20.0)  # Database timeout
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimiter, limit=1000, window=60)  # Increased limit to handle multiple components polling
app.add_middleware(FileUploadConfig, max_upload_size=30 * 1024 * 1024, upload_timeout=120.0)  # 30MB file upload limit

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In development, allow all origins
    allow_credentials=False,  # Set to False when using allow_origins=["*"]
    allow_methods=["*"],
    allow_headers=["*"]
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_PREFIX)

@app.get("/")
async def root():
    return {
        "message": "Welcome to CampusPQ API",
        "docs": f"{settings.API_V1_PREFIX}/docs"
    }

@app.get("/api/v1/health")
async def health_check():
    """Enhanced health check endpoint with comprehensive monitoring"""
    from app.services.health_monitor import health_monitor

    try:
        health_status = await health_monitor.comprehensive_health_check()

        # Return appropriate HTTP status code based on health
        status_code = 200
        if health_status["status"] == "unhealthy":
            status_code = 503  # Service Unavailable
        elif health_status["status"] == "error":
            status_code = 500  # Internal Server Error

        # Add basic service info
        health_status.update({
            "service": "campuspq-api",
            "version": "1.0.0"
        })

        return health_status

    except Exception as e:
        # Fallback to simple health check if monitoring fails
        import time
        return {
            "status": "degraded",
            "service": "campuspq-api",
            "timestamp": int(time.time()),
            "version": "1.0.0",
            "error": f"Health monitoring failed: {str(e)}"
        }

@app.get("/health")
async def simple_health_check():
    """Simple health check endpoint (alternative path)"""
    return {"status": "ok"}

@app.get("/api/v1/health/issues")
async def health_issues():
    """Endpoint to check for hanging issues and system problems"""
    from app.services.health_monitor import health_monitor

    try:
        issues = await health_monitor.detect_hanging_issues()
        return {
            "timestamp": time.time(),
            "issues_count": len(issues),
            "issues": issues
        }
    except Exception as e:
        return {
            "timestamp": time.time(),
            "error": f"Failed to check for issues: {str(e)}",
            "issues_count": 0,
            "issues": []
        }

# Startup event handler
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger = logging.getLogger(__name__)
    logger.info("Starting application initialization...")

    try:
        success = initialize_application()
        if not success:
            logger.error("Application initialization failed!")
            handle_startup_failure()
        else:
            logger.info("Application initialized successfully!")
    except Exception as e:
        logger.error(f"Startup error: {e}")
        handle_startup_failure()

# Health check endpoint with database status
@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check including database status"""
    return get_application_status()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
