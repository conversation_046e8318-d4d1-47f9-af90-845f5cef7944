from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

# Configure connection pool with optimized settings for better performance
# Balanced settings for local PostgreSQL with proper connection management
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_size=5,  # Increased pool size for better concurrency
    max_overflow=10,  # Allow more overflow connections
    pool_timeout=30,  # Reduced timeout to fail fast instead of hanging
    pool_recycle=3600,  # Recycle connections every hour
    echo=False,  # Set to True for SQL debugging
    # Add connection timeout to prevent hanging
    connect_args={
        "connect_timeout": 10,  # 10 second connection timeout
        "application_name": "campuspq_backend"
    }
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    """
    FastAPI dependency for database sessions with timeout protection.
    """
    db = SessionLocal()
    try:
        # Set statement timeout to prevent hanging queries
        db.execute("SET statement_timeout = '30s'")
        yield db
    except Exception as e:
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Database session error: {str(e)}")
        try:
            db.rollback()
        except:
            pass  # Ignore rollback errors
        raise e
    finally:
        try:
            db.close()
        except:
            pass  # Ignore close errors during cleanup


async def get_db_async():
    """
    Async database dependency with timeout protection.
    """
    import asyncio

    db = SessionLocal()
    try:
        # Set statement timeout to prevent hanging queries
        db.execute("SET statement_timeout = '30s'")

        # Wrap in asyncio timeout to prevent hanging
        async def db_wrapper():
            yield db

        async for session in db_wrapper():
            yield session

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Async database session error: {str(e)}")
        try:
            db.rollback()
        except:
            pass
        raise e
    finally:
        try:
            db.close()
        except:
            pass


def get_db_session():
    """
    Context manager for database sessions with proper cleanup and timeout handling.
    Use this in background tasks to ensure connections are properly released.
    """
    from contextlib import contextmanager
    import signal
    import time

    @contextmanager
    def session_context():
        db = None
        start_time = time.time()
        try:
            # Add timeout for session creation
            db = SessionLocal()

            # Set statement timeout to prevent hanging queries
            db.execute("SET statement_timeout = '30s'")

            yield db

            # Check if session took too long
            elapsed = time.time() - start_time
            if elapsed > 30:  # 30 second warning threshold
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Database session took {elapsed:.2f}s - potential performance issue")

        except Exception as e:
            if db:
                try:
                    db.rollback()
                except:
                    pass  # Ignore rollback errors during cleanup
            raise e
        finally:
            if db:
                try:
                    db.close()
                except:
                    pass  # Ignore close errors during cleanup

    return session_context()
