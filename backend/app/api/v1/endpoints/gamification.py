from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import text

from app import schemas
from app.api import deps
from app.models.user import User, UserRole
from app.services import gamification_service

router = APIRouter()


@router.get("/badges", response_model=List[Dict[str, Any]])
def get_user_badges(
    *,
    db: Session = Depends(deps.get_db),
    user_id: Optional[int] = Query(None, description="User ID (defaults to current user)"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get badges earned by a user.
    """
    # If no user_id is provided, use the current user's ID
    if user_id is None:
        user_id = current_user.id
    # Only allow students to view their own badges
    elif current_user.role == UserRole.STUDENT and user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Students can only view their own badges"
        )

    return gamification_service.get_user_badges(db, user_id=user_id)


@router.get("/points", response_model=int)
def get_user_points(
    *,
    db: Session = Depends(deps.get_db),
    user_id: Optional[int] = Query(None, description="User ID (defaults to current user)"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get total points for a user.
    """
    # If no user_id is provided, use the current user's ID
    if user_id is None:
        user_id = current_user.id
    # Only allow students to view their own points
    elif current_user.role == UserRole.STUDENT and user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Students can only view their own points"
        )

    return gamification_service.get_user_points(db, user_id=user_id)


@router.get("/profile", response_model=schemas.UserGamificationProfile)
def get_gamification_profile(
    *,
    db: Session = Depends(deps.get_db),
    user_id: Optional[int] = Query(None, description="User ID (defaults to current user)"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get complete gamification profile for a user.
    """
    # If no user_id is provided, use the current user's ID
    if user_id is None:
        user_id = current_user.id
    # Only allow students to view their own profile
    elif current_user.role == UserRole.STUDENT and user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Students can only view their own gamification profile"
        )

    # Get user's total points
    total_points = gamification_service.get_user_points(db, user_id=user_id)

    # Get user's current level
    current_level = gamification_service.get_level_for_points(total_points)

    # Get user's badges
    badges = gamification_service.get_user_badges(db, user_id=user_id)

    # Get user's streak
    streak = db.query(gamification_service.UserStreak).filter(
        gamification_service.UserStreak.user_id == user_id
    ).first()

    # Calculate next level and points needed
    next_level = None
    points_to_next_level = None

    for i, level in enumerate(gamification_service.LEVELS):
        if level["level_number"] == current_level["level_number"] and i < len(gamification_service.LEVELS) - 1:
            next_level = gamification_service.LEVELS[i + 1]
            points_to_next_level = next_level["min_points"] - total_points
            break

    return {
        "total_points": total_points,
        "current_level": current_level,
        "badges": badges,
        "streak": streak,
        "next_level": next_level,
        "points_to_next_level": points_to_next_level
    }


@router.get("/leaderboard", response_model=schemas.Leaderboard)
def get_leaderboard(
    *,
    db: Session = Depends(deps.get_db),
    limit: int = Query(10, description="Number of users to include in the leaderboard"),
    school_id: Optional[int] = Query(None, description="Filter by school ID"),
    department_id: Optional[int] = Query(None, description="Filter by department ID"),
    course_id: Optional[int] = Query(None, description="Filter by course ID"),
    time_period: Optional[str] = Query(None, description="Filter by time period ('daily', 'weekly', 'all_time')"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get the points leaderboard with optional filtering.

    - **limit**: Number of users to include in the leaderboard
    - **school_id**: Filter by school ID
    - **department_id**: Filter by department ID
    - **course_id**: Filter by course ID
    - **time_period**: Filter by time period ('daily', 'weekly', 'all_time')
    """
    try:
        # Get leaderboard entries using the service
        leaderboard_entries = gamification_service.get_leaderboard(
            db,
            limit=limit,
            school_id=school_id,
            department_id=department_id,
            course_id=course_id,
            time_period=time_period
        )

        # Return the leaderboard entries
        return {"entries": leaderboard_entries}

    except Exception as e:
        print(f"Error in get_leaderboard endpoint: {e}")
        # Rollback any pending transaction
        try:
            db.rollback()
        except Exception as rollback_error:
            print(f"Error during rollback: {rollback_error}")

        # Return empty leaderboard to prevent UI errors
        return {"entries": []}


@router.post("/update-streak", response_model=schemas.UserStreak)
def update_user_streak(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a user's learning streak.
    This endpoint should be called whenever a user performs a learning activity.
    """
    return gamification_service.update_streak(db, user_id=current_user.id)


@router.get("/recent-activity")
def get_recent_activity(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    limit: int = Query(5, description="Number of activities to return"),
) -> Any:
    """
    Get recent activity for the current user.
    """
    try:
        activities = []

        # Get recent points transactions
        from app.models.gamification import PointsTransaction
        recent_points = db.query(PointsTransaction).filter(
            PointsTransaction.user_id == current_user.id
        ).order_by(PointsTransaction.created_at.desc()).limit(limit).all()

        for transaction in recent_points:
            activities.append({
                "id": f"points_{transaction.id}",
                "type": "achievement" if transaction.points >= 50 else "quiz",
                "title": transaction.description,
                "description": f"Earned {transaction.points} points",
                "timestamp": transaction.created_at.isoformat(),
                "points": transaction.points
            })

        # Sort by timestamp and limit
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        activities = activities[:limit]

        return {"activities": activities}

    except Exception as e:
        print(f"Error getting recent activity: {e}")
        return {"activities": []}
